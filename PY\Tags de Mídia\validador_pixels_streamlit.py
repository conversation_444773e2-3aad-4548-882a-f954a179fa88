import streamlit as st
from playwright.async_api import async_playwright
import urllib.parse
import asyncio
import threading
import sys
from datetime import datetime

# URLs típicas dos pixels
PIXEL_PATTERNS = {
    'facebook': 'https://www.facebook.com/tr',
    'google_ads': 'https://www.googleadservices.com/pagead/conversion',
    'google_analytics': 'https://www.google-analytics.com/collect',
    'google_gtag': 'https://www.googletagmanager.com/gtag/js',
    'tiktok': 'https://analytics.tiktok.com/api/v2/pixel',
    'pinterest': 'https://ct.pinterest.com/v3/',
    'snapchat': 'https://tr.snapchat.com/p',
    'twitter': 'https://t.co/i/adsct',
    'linkedin': 'https://px.ads.linkedin.com/collect'
}

def parse_query_params(url):
    """Extrai parâmetros da query string de uma URL"""
    parsed = urllib.parse.urlparse(url)
    return dict(urllib.parse.parse_qsl(parsed.query))

def format_event_data(pixel_name, url, params):
    """Formata os dados do evento para exibição"""
    return {
        'timestamp': datetime.now().strftime('%H:%M:%S'),
        'pixel': pixel_name.upper(),
        'url': url,
        'params': params
    }

async def run_shopify_test_async(store_url, selected_pixels, progress_callback=None, event_callback=None):
    """
    Executa o teste de pixels do Shopify de forma assíncrona

    Args:
        store_url: URL da loja Shopify
        selected_pixels: Lista de pixels para monitorar
        progress_callback: Função para atualizar progresso
        event_callback: Função para reportar eventos detectados
    """
    detected_events = []

    # Filtra apenas os pixels selecionados
    active_patterns = {k: v for k, v in PIXEL_PATTERNS.items() if k in selected_pixels}

    try:
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context()
            page = await context.new_page()

            async def intercept_request(route, request):
                url = request.url
                for nome, padrao in active_patterns.items():
                    if padrao in url:
                        params = parse_query_params(url)
                        event_data = format_event_data(nome, url, params)
                        detected_events.append(event_data)

                        if event_callback:
                            event_callback(event_data)

                await route.continue_()

            await page.route("**/*", intercept_request)

            # 1. Acessa a home da loja
            if progress_callback:
                progress_callback("🔍 Acessando a página inicial da loja...")

            await page.goto(store_url, wait_until="domcontentloaded", timeout=30000)
            await page.wait_for_timeout(3000)

            # 2. Tenta acessar um produto
            if progress_callback:
                progress_callback("🛍️ Procurando produtos na loja...")

            try:
                produto = page.locator("a[href*='/products/']").first
                if await produto.count() > 0:
                    await produto.click()
                    await page.wait_for_timeout(3000)

                    if progress_callback:
                        progress_callback("📦 Produto acessado com sucesso!")
                else:
                    if progress_callback:
                        progress_callback("⚠️ Nenhum produto encontrado na página inicial")
            except Exception as e:
                if progress_callback:
                    progress_callback(f"⚠️ Erro ao acessar produto: {str(e)}")

            # 3. Tenta adicionar ao carrinho
            if progress_callback:
                progress_callback("🛒 Tentando adicionar produto ao carrinho...")

            try:
                # Tenta diferentes seletores para o botão de adicionar ao carrinho
                selectors = [
                    "form[action*='/cart'] [type=submit]",
                    "[data-testid='add-to-cart']",
                    ".btn-cart",
                    ".add-to-cart",
                    "button[name='add']"
                ]

                button_found = False
                for selector in selectors:
                    add_to_cart = page.locator(selector).first
                    if await add_to_cart.count() > 0:
                        await add_to_cart.click()
                        await page.wait_for_timeout(5000)
                        button_found = True
                        if progress_callback:
                            progress_callback("✅ Produto adicionado ao carrinho!")
                        break

                if not button_found:
                    if progress_callback:
                        progress_callback("⚠️ Botão de adicionar ao carrinho não encontrado")

            except Exception as e:
                if progress_callback:
                    progress_callback(f"⚠️ Erro ao adicionar ao carrinho: {str(e)}")

            # 4. Aguarda um pouco mais para capturar eventos tardios
            if progress_callback:
                progress_callback("⏳ Aguardando eventos adicionais...")
            await page.wait_for_timeout(3000)

            if progress_callback:
                progress_callback("✅ Teste concluído!")

            await browser.close()

    except Exception as e:
        if progress_callback:
            progress_callback(f"❌ Erro durante o teste: {str(e)}")

    return detected_events

def run_shopify_test(store_url, selected_pixels, progress_callback=None, event_callback=None):
    """Executa o teste usando threading para evitar problemas com asyncio no Streamlit"""
    import subprocess
    import json
    import tempfile
    import os

    detected_events = []

    try:
        # Cria um script temporário para executar o teste
        script_content = f'''
import asyncio
import json
import sys
from playwright.async_api import async_playwright
import urllib.parse
from datetime import datetime

PIXEL_PATTERNS = {PIXEL_PATTERNS}
selected_pixels = {selected_pixels}

def parse_query_params(url):
    parsed = urllib.parse.urlparse(url)
    return dict(urllib.parse.parse_qsl(parsed.query))

def format_event_data(pixel_name, url, params):
    return {{
        'timestamp': datetime.now().strftime('%H:%M:%S'),
        'pixel': pixel_name.upper(),
        'url': url,
        'params': params
    }}

async def main():
    detected_events = []
    active_patterns = {{k: v for k, v in PIXEL_PATTERNS.items() if k in selected_pixels}}

    try:
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context()
            page = await context.new_page()

            async def intercept_request(route, request):
                url = request.url
                for nome, padrao in active_patterns.items():
                    if padrao in url:
                        params = parse_query_params(url)
                        event_data = format_event_data(nome, url, params)
                        detected_events.append(event_data)
                        print(f"EVENT_DETECTED:{{json.dumps(event_data)}}")
                await route.continue_()

            await page.route("**/*", intercept_request)

            print("STATUS:🔍 Acessando a página inicial da loja...")
            await page.goto("{store_url}", wait_until="domcontentloaded", timeout=30000)
            await page.wait_for_timeout(3000)

            print("STATUS:🛍️ Procurando produtos na loja...")
            try:
                produto = page.locator("a[href*='/products/']").first
                if await produto.count() > 0:
                    await produto.click()
                    await page.wait_for_timeout(3000)
                    print("STATUS:📦 Produto acessado com sucesso!")
                else:
                    print("STATUS:⚠️ Nenhum produto encontrado na página inicial")
            except Exception as e:
                print(f"STATUS:⚠️ Erro ao acessar produto: {{str(e)}}")

            print("STATUS:🛒 Tentando adicionar produto ao carrinho...")
            try:
                selectors = [
                    "form[action*='/cart'] [type=submit]",
                    "[data-testid='add-to-cart']",
                    ".btn-cart",
                    ".add-to-cart",
                    "button[name='add']"
                ]

                button_found = False
                for selector in selectors:
                    add_to_cart = page.locator(selector).first
                    if await add_to_cart.count() > 0:
                        await add_to_cart.click()
                        await page.wait_for_timeout(5000)
                        button_found = True
                        print("STATUS:✅ Produto adicionado ao carrinho!")
                        break

                if not button_found:
                    print("STATUS:⚠️ Botão de adicionar ao carrinho não encontrado")

            except Exception as e:
                print(f"STATUS:⚠️ Erro ao adicionar ao carrinho: {{str(e)}}")

            print("STATUS:⏳ Aguardando eventos adicionais...")
            await page.wait_for_timeout(3000)
            print("STATUS:✅ Teste concluído!")

            await browser.close()

    except Exception as e:
        print(f"STATUS:❌ Erro durante o teste: {{str(e)}}")

    print(f"FINAL_RESULT:{{json.dumps(detected_events)}}")

if __name__ == "__main__":
    asyncio.run(main())
'''

        # Salva o script temporário
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(script_content)
            temp_script = f.name

        try:
            # Executa o script
            process = subprocess.Popen(
                [sys.executable, temp_script],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            # Lê a saída linha por linha
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    line = output.strip()
                    if line.startswith("STATUS:"):
                        status = line[7:]  # Remove "STATUS:"
                        if progress_callback:
                            progress_callback(status)
                    elif line.startswith("EVENT_DETECTED:"):
                        event_json = line[15:]  # Remove "EVENT_DETECTED:"
                        try:
                            event_data = json.loads(event_json)
                            detected_events.append(event_data)
                            if event_callback:
                                event_callback(event_data)
                        except:
                            pass
                    elif line.startswith("FINAL_RESULT:"):
                        result_json = line[13:]  # Remove "FINAL_RESULT:"
                        try:
                            final_events = json.loads(result_json)
                            detected_events.extend([e for e in final_events if e not in detected_events])
                        except:
                            pass

            # Aguarda o processo terminar
            process.wait()

        finally:
            # Remove o arquivo temporário
            try:
                os.unlink(temp_script)
            except:
                pass

    except Exception as e:
        if progress_callback:
            progress_callback(f"❌ Erro durante o teste: {str(e)}")

    return detected_events

def main():
    st.set_page_config(
        page_title="Validador de Pixels Shopify",
        page_icon="🔍",
        layout="wide"
    )
    
    st.title("🔍 Validador de Pixels Shopify")
    st.markdown("**Ferramenta para detectar e validar pixels de rastreamento em lojas Shopify**")
    
    # Sidebar com configurações
    with st.sidebar:
        st.header("⚙️ Configurações")
        
        # URL da loja
        store_url = st.text_input(
            "🏪 URL da Loja Shopify",
            placeholder="https://minhaloja.myshopify.com",
            help="Digite a URL completa da loja Shopify que deseja testar"
        )
        
        # Seleção de pixels
        st.subheader("📡 Pixels para Monitorar")
        selected_pixels = []
        
        for pixel_name, pixel_url in PIXEL_PATTERNS.items():
            if st.checkbox(pixel_name.replace('_', ' ').title(), value=True):
                selected_pixels.append(pixel_name)
        
        # Botão para iniciar teste
        start_test = st.button("🚀 Iniciar Teste", type="primary", use_container_width=True)
    
    # Área principal
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.subheader("📊 Status do Teste")
        status_container = st.empty()
        progress_container = st.empty()
    
    with col2:
        st.subheader("📡 Eventos Detectados")
        events_container = st.empty()
    
    # Inicializa session state
    if 'events' not in st.session_state:
        st.session_state.events = []
    if 'test_running' not in st.session_state:
        st.session_state.test_running = False
    
    # Executa o teste
    if start_test and store_url and selected_pixels and not st.session_state.test_running:
        if not store_url.startswith(('http://', 'https://')):
            st.error("❌ Por favor, insira uma URL válida (deve começar com http:// ou https://)")
        else:
            st.session_state.test_running = True
            st.session_state.events = []
            
            # Containers para atualizações em tempo real
            status_placeholder = status_container.empty()
            progress_placeholder = progress_container.empty()
            events_placeholder = events_container.empty()
            
            def update_progress(message):
                status_placeholder.info(message)
            
            def update_events(event_data):
                st.session_state.events.append(event_data)
                
                # Atualiza a exibição de eventos
                if st.session_state.events:
                    events_df = []
                    for event in st.session_state.events:
                        events_df.append({
                            'Horário': event['timestamp'],
                            'Pixel': event['pixel'],
                            'Parâmetros': len(event['params'])
                        })
                    
                    events_placeholder.dataframe(events_df, use_container_width=True)
                else:
                    events_placeholder.info("Nenhum evento detectado ainda...")
            
            # Executa o teste
            try:
                detected_events = run_shopify_test(
                    store_url, 
                    selected_pixels, 
                    progress_callback=update_progress,
                    event_callback=update_events
                )
                
                st.session_state.test_running = False
                
                # Exibe resultados finais
                if detected_events:
                    status_placeholder.success(f"✅ Teste concluído! {len(detected_events)} eventos detectados.")
                    
                    # Exibe detalhes dos eventos
                    st.subheader("📋 Detalhes dos Eventos")
                    
                    for i, event in enumerate(detected_events):
                        with st.expander(f"🔍 {event['pixel']} - {event['timestamp']}"):
                            st.write(f"**URL:** {event['url']}")
                            if event['params']:
                                st.write("**Parâmetros:**")
                                for key, value in event['params'].items():
                                    st.write(f"• **{key}:** {value}")
                            else:
                                st.write("*Nenhum parâmetro detectado*")
                else:
                    status_placeholder.warning("⚠️ Teste concluído, mas nenhum pixel foi detectado.")
                    
            except Exception as e:
                st.session_state.test_running = False
                status_placeholder.error(f"❌ Erro durante o teste: {str(e)}")
    
    elif start_test and not store_url:
        st.error("❌ Por favor, insira a URL da loja Shopify")
    elif start_test and not selected_pixels:
        st.error("❌ Por favor, selecione pelo menos um pixel para monitorar")
    elif start_test and st.session_state.test_running:
        st.warning("⏳ Um teste já está em execução. Aguarde a conclusão.")

    # Informações adicionais
    with st.expander("ℹ️ Como usar esta ferramenta"):
        st.markdown("""
        ### 📖 Instruções de Uso

        1. **Configure a URL**: Digite a URL completa da loja Shopify que deseja testar
        2. **Selecione os Pixels**: Marque quais pixels de rastreamento você quer monitorar
        3. **Execute o Teste**: Clique em "Iniciar Teste" para começar a análise

        ### 🔍 O que a ferramenta faz

        - Acessa a página inicial da loja
        - Navega para um produto (se disponível)
        - Tenta adicionar o produto ao carrinho
        - Monitora todas as requisições de rede em busca dos pixels selecionados
        - Captura e exibe os parâmetros enviados para cada pixel

        ### 📡 Pixels Suportados

        - **Facebook Pixel**: Rastreamento de conversões do Facebook/Meta
        - **Google Ads**: Conversões do Google Ads
        - **Google Analytics**: Eventos do Google Analytics
        - **Google Gtag**: Google Tag Manager
        - **TikTok Pixel**: Rastreamento do TikTok Ads
        - **Pinterest**: Conversões do Pinterest
        - **Snapchat**: Pixel do Snapchat Ads
        - **Twitter**: Rastreamento do Twitter Ads
        - **LinkedIn**: Conversões do LinkedIn Ads

        ### 💡 Dicas

        - Use URLs de lojas reais para obter resultados mais precisos
        - Alguns pixels podem demorar alguns segundos para disparar
        - Se nenhum pixel for detectado, verifique se a loja realmente possui os pixels instalados
        - A ferramenta funciona melhor com lojas que possuem produtos disponíveis
        """)

if __name__ == "__main__":
    main()

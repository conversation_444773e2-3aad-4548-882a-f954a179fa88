import streamlit as st
import subprocess
import json
import tempfile
import os
import sys
import time
from datetime import datetime

# URLs típicas dos pixels
PIXEL_PATTERNS = {
    'facebook': 'https://www.facebook.com/tr',
    'google_ads': 'https://www.googleadservices.com/pagead/conversion',
    'google_analytics': 'https://www.google-analytics.com/collect',
    'google_gtag': 'https://www.googletagmanager.com/gtag/js',
    'tiktok': 'https://analytics.tiktok.com/api/v2/pixel',
    'pinterest': 'https://ct.pinterest.com/v3/',
    'snapchat': 'https://tr.snapchat.com/p',
    'twitter': 'https://t.co/i/adsct',
    'linkedin': 'https://px.ads.linkedin.com/collect'
}

def create_test_script(store_url, selected_pixels):
    """Cria um script Python separado para executar o teste"""
    script_content = f'''
import sys
import json
import traceback
from playwright.sync_api import sync_playwright
import urllib.parse
from datetime import datetime

# Configurações
PIXEL_PATTERNS = {PIXEL_PATTERNS}
selected_pixels = {selected_pixels}
store_url = "{store_url}"

def parse_query_params(url):
    try:
        parsed = urllib.parse.urlparse(url)
        return dict(urllib.parse.parse_qsl(parsed.query))
    except:
        return {{}}

def format_event_data(pixel_name, url, params):
    return {{
        'timestamp': datetime.now().strftime('%H:%M:%S'),
        'pixel': pixel_name.upper(),
        'url': url,
        'params': params
    }}

def main():
    detected_events = []
    active_patterns = {{k: v for k, v in PIXEL_PATTERNS.items() if k in selected_pixels}}
    
    print("STATUS:🔧 Iniciando navegador...")
    
    try:
        with sync_playwright() as p:
            print("STATUS:🌐 Abrindo navegador Chromium...")
            
            browser = p.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox', 
                    '--disable-dev-shm-usage', 
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            context = browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                viewport={{'width': 1920, 'height': 1080}},
                ignore_https_errors=True
            )
            page = context.new_page()
            print("STATUS:✅ Navegador iniciado com sucesso")

            def intercept_request(route, request):
                try:
                    url = request.url
                    for nome, padrao in active_patterns.items():
                        if padrao in url:
                            params = parse_query_params(url)
                            event_data = format_event_data(nome, url, params)
                            detected_events.append(event_data)
                            print(f"EVENT:{{json.dumps(event_data)}}")
                    route.continue_()
                except Exception as e:
                    print(f"STATUS:⚠️ Erro na interceptação: {{str(e)}}")
                    route.continue_()

            page.route("**/*", intercept_request)
            print("STATUS:✅ Interceptação configurada")
            
            print("STATUS:🔍 Acessando a página inicial da loja...")
            response = page.goto(store_url, wait_until="domcontentloaded", timeout=60000)
            
            if response:
                print(f"STATUS:✅ Página carregada (HTTP {{response.status}})")
            
            page.wait_for_timeout(5000)
            
            title = page.title()
            print(f"STATUS:📄 Título: {{title[:50]}}...")
            
            print("STATUS:🛍️ Procurando produtos...")
            product_selectors = [
                "a[href*='/products/']",
                "a[href*='/product/']", 
                ".product-item a",
                ".product a"
            ]
            
            produto_encontrado = False
            for selector in product_selectors:
                produtos = page.locator(selector)
                count = produtos.count()
                if count > 0:
                    print(f"STATUS:📦 Encontrados {{count}} produtos")
                    produtos.first.click()
                    page.wait_for_timeout(5000)
                    produto_encontrado = True
                    print("STATUS:✅ Produto acessado!")
                    break
            
            if not produto_encontrado:
                print("STATUS:⚠️ Nenhum produto encontrado")
            
            print("STATUS:🛒 Procurando botão de carrinho...")
            cart_selectors = [
                "form[action*='/cart'] [type=submit]",
                "form[action*='/cart'] button",
                "[data-testid='add-to-cart']",
                ".btn-cart",
                ".add-to-cart",
                "button[name='add']",
                ".product-form button[type='submit']"
            ]
            
            button_found = False
            for selector in cart_selectors:
                try:
                    buttons = page.locator(selector)
                    count = buttons.count()
                    if count > 0:
                        print(f"STATUS:🎯 Botão encontrado: {{selector}}")
                        buttons.first.click()
                        page.wait_for_timeout(7000)
                        button_found = True
                        print("STATUS:✅ Clicou no botão!")
                        break
                except:
                    continue
            
            if not button_found:
                print("STATUS:⚠️ Botão de carrinho não encontrado")
            
            print("STATUS:⏳ Aguardando eventos finais...")
            page.wait_for_timeout(5000)
            
            print("STATUS:🔄 Fazendo scroll...")
            try:
                page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                page.wait_for_timeout(3000)
                page.evaluate("window.scrollTo(0, 0)")
                page.wait_for_timeout(2000)
            except:
                pass
            
            print("STATUS:✅ Teste concluído!")
            browser.close()
            
    except Exception as e:
        print(f"STATUS:❌ Erro: {{str(e)}}")
        print(f"STATUS:🔍 Detalhes: {{traceback.format_exc()[:500]}}")
    
    print(f"FINAL:{{json.dumps(detected_events)}}")

if __name__ == "__main__":
    main()
'''
    return script_content

def run_pixel_test(store_url, selected_pixels):
    """Executa o teste de pixels usando subprocess"""
    detected_events = []
    status_messages = []
    
    try:
        # Cria script temporário
        script_content = create_test_script(store_url, selected_pixels)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
            f.write(script_content)
            temp_script = f.name
        
        try:
            # Executa o script
            process = subprocess.Popen(
                [sys.executable, temp_script],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                universal_newlines=True,
                encoding='utf-8'
            )
            
            # Lê a saída
            stdout, stderr = process.communicate(timeout=120)
            
            # Processa as linhas de saída
            for line in stdout.split('\\n'):
                line = line.strip()
                if line.startswith("STATUS:"):
                    status_messages.append(line[7:])
                elif line.startswith("EVENT:"):
                    try:
                        event_data = json.loads(line[6:])
                        detected_events.append(event_data)
                    except:
                        pass
                elif line.startswith("FINAL:"):
                    try:
                        final_events = json.loads(line[6:])
                        for event in final_events:
                            if event not in detected_events:
                                detected_events.append(event)
                    except:
                        pass
            
            if stderr:
                status_messages.append(f"⚠️ Avisos: {stderr[:200]}...")
                
        finally:
            # Remove arquivo temporário
            try:
                os.unlink(temp_script)
            except:
                pass
                
    except subprocess.TimeoutExpired:
        status_messages.append("⏰ Teste interrompido por timeout")
    except Exception as e:
        status_messages.append(f"❌ Erro: {str(e)}")
    
    return detected_events, status_messages

def main():
    st.set_page_config(
        page_title="Validador de Pixels Shopify",
        page_icon="🔍",
        layout="wide"
    )
    
    st.title("🔍 Validador de Pixels Shopify")
    st.markdown("**Ferramenta para detectar e validar pixels de rastreamento em lojas Shopify**")
    
    # Sidebar
    with st.sidebar:
        st.header("⚙️ Configurações")
        
        store_url = st.text_input(
            "🏪 URL da Loja Shopify",
            placeholder="https://zerezes.com.br",
            help="Digite a URL completa da loja que deseja testar"
        )
        
        st.subheader("📡 Pixels para Monitorar")
        selected_pixels = []
        
        for pixel_name, pixel_url in PIXEL_PATTERNS.items():
            if st.checkbox(pixel_name.replace('_', ' ').title(), value=True):
                selected_pixels.append(pixel_name)
        
        start_test = st.button("🚀 Iniciar Teste", type="primary", use_container_width=True)
    
    # Área principal
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.subheader("📊 Status do Teste")
        status_container = st.empty()
    
    with col2:
        st.subheader("📡 Eventos Detectados")
        events_container = st.empty()
    
    # Executa teste
    if start_test and store_url and selected_pixels:
        if not store_url.startswith(('http://', 'https://')):
            st.error("❌ URL deve começar com http:// ou https://")
        else:
            with st.spinner("🔄 Executando teste..."):
                detected_events, status_messages = run_pixel_test(store_url, selected_pixels)
            
            # Exibe status
            if status_messages:
                status_text = "\\n".join(status_messages[-10:])  # Últimas 10 mensagens
                status_container.text_area("📋 Log do Teste", status_text, height=300)
            
            # Exibe eventos
            if detected_events:
                st.success(f"✅ {len(detected_events)} pixels detectados!")
                
                # Tabela resumo
                events_df = []
                for event in detected_events:
                    events_df.append({
                        'Horário': event['timestamp'],
                        'Pixel': event['pixel'],
                        'Parâmetros': len(event['params'])
                    })
                events_container.dataframe(events_df, use_container_width=True)
                
                # Detalhes
                st.subheader("📋 Detalhes dos Eventos")
                for i, event in enumerate(detected_events):
                    with st.expander(f"🔍 {event['pixel']} - {event['timestamp']}"):
                        st.write(f"**URL:** {event['url']}")
                        if event['params']:
                            st.write("**Parâmetros:**")
                            for key, value in event['params'].items():
                                st.write(f"• **{key}:** {value}")
                        else:
                            st.write("*Nenhum parâmetro detectado*")
            else:
                st.warning("⚠️ Nenhum pixel foi detectado. Verifique se a loja possui os pixels instalados.")
    
    elif start_test and not store_url:
        st.error("❌ Por favor, insira a URL da loja")
    elif start_test and not selected_pixels:
        st.error("❌ Por favor, selecione pelo menos um pixel")
    
    # Informações
    with st.expander("ℹ️ Como usar"):
        st.markdown("""
        ### 📖 Instruções
        1. Digite a URL da loja Shopify
        2. Selecione os pixels que quer monitorar
        3. Clique em "Iniciar Teste"
        
        ### 🔍 O que faz
        - Acessa a página inicial
        - Navega para um produto
        - Tenta adicionar ao carrinho
        - Monitora requisições de pixels
        
        ### 📡 Pixels Suportados
        - Facebook, Google Ads, Google Analytics
        - TikTok, Pinterest, Snapchat
        - Twitter, LinkedIn
        """)

if __name__ == "__main__":
    main()
